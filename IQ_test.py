import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import io
import os
import argparse
import time
from tqdm import tqdm

# 解决控制台输出编码问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 尝试导入TensorFlow
try:
    import tensorflow as tf
    from tensorflow.keras.models import Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, LSTM, Flatten
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow。")
except ImportError:
    USE_TENSORFLOW = False
    print("警告：未能加载TensorFlow。")

# ==============================================================================
# 1. 平台运动与几何关系 (Platform Motion and Geometry)
# ==============================================================================

def weave(g, T, del_T, alt_kft, vel):
    """(保持不变) 平台导航数据生成函数"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    alt0 = alt_kft * 1000 * 0.3048
    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    px, py, pz = 0, 0, alt0
    XYZA, XYZADOT = [], []
    itt = 0
    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)
        if itt > 0:
            vz = XYZADOT[-1][2]
            px += vx * dtt
            py += vy * dtt
        else:
            vz = 0
        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
        itt += 1
    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    long_vect = np.vstack([np.sin(hdg), np.cos(hdg), np.zeros_like(hdg)])
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / (vect_norms + 1e-9)
    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect

# ==============================================================================
# 2. IQ数据生成与信号处理 (IQ Data Generation and Signal Processing)
# ==============================================================================

def generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db, L=2, d=0.5, fs=2e4, num_samples=2048):
    """最简化的IQ数据生成 - 只包含核心多普勒和AOA效应"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_true = p_true
    Px, Py, Pz = platform_pos

    # 基本几何计算
    R_vec = np.array([Px - xe, Py - ye, Pz - ze])
    R = np.linalg.norm(R_vec)
    los_vec_norm = R_vec / R

    # 核心物理效应1：多普勒频移
    relative_velocity = np.dot(platform_vel, los_vec_norm)
    doppler_freq = -(fo_true / c) * relative_velocity

    # 核心物理效应2：AOA相位差
    cos_theta = -np.dot(los_vec_norm, mu_vect_single)

    # 生成纯净的IQ信号
    t = np.arange(num_samples) / fs
    iq_data = np.zeros((L, num_samples), dtype=np.complex128)

    for i in range(L):
        # 阵列元素间的相位差（基于AOA）
        phase_shift = (2 * np.pi * d * i / lambda_) * cos_theta

        # 生成基带信号：多普勒频移 + 阵列相位差
        signal = np.exp(1j * (2 * np.pi * doppler_freq * t + phase_shift))
        iq_data[i, :] = signal

    # 添加高斯白噪声（最简单的噪声模型）
    snr_linear = 10**(snr_db / 10.0)
    signal_power = 1.0
    noise_power = signal_power / snr_linear
    noise_std = np.sqrt(noise_power / 2)

    # 独立的高斯白噪声
    noise = (np.random.normal(0, noise_std, size=iq_data.shape) +
             1j * np.random.normal(0, noise_std, size=iq_data.shape))

    return iq_data + noise

def extract_features_from_iq(iq_data, fo, fs):
    """(保持不变) 提取稳定、无模糊的特征"""
    L, num_samples = iq_data.shape
    avg_signal = np.mean(iq_data, axis=0)
    fft_result = np.fft.fft(avg_signal)
    fft_freqs = np.fft.fftfreq(num_samples, 1/fs)
    peak_index = np.argmax(np.abs(fft_result))
    doppler_est = fft_freqs[peak_index]
    f_est = fo + doppler_est
    if L >= 2:
        cross_correlation = np.mean(iq_data[1, :] * np.conj(iq_data[0, :]))
        norm_factor = np.abs(cross_correlation)
        normalized_correlation = cross_correlation / (norm_factor + 1e-9)
        corr_real = normalized_correlation.real
        corr_imag = normalized_correlation.imag
    else:
        corr_real, corr_imag = 0.0, 0.0
    return f_est, corr_real, corr_imag

# ==============================================================================
# 3. 传统求解器 (Traditional Solver - Ported from MATLAB)
# ==============================================================================

def traditional_solver(measurement_obs, Plat_Nav_Data, mu_vect, fo, L=1.0, d=0.5):
    """
    改进的传统多普勒定位求解器 - 专注于多普勒信息
    """
    try:
        c = 2.998e8
        f_obs = measurement_obs[:, 0]
        Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data

        # 改进的初始估计策略
        center_x, center_y, center_z = np.mean(Px), np.mean(Py), np.mean(Pz)

        # 基于多普勒信息的初始估计
        avg_doppler = np.mean(f_obs - fo)
        avg_velocity = np.array([np.mean(Vx), np.mean(Vy), np.mean(Vz)])
        vel_magnitude = np.linalg.norm(avg_velocity)

        # 估计目标大致位置
        if abs(avg_doppler) > 1 and vel_magnitude > 0:
            # 基于多普勒符号估计方向
            if avg_doppler > 0:
                # 正多普勒：目标在平台前方
                direction = avg_velocity / vel_magnitude
            else:
                # 负多普勒：目标在平台后方
                direction = -avg_velocity / vel_magnitude

            # 估计距离（基于多普勒大小）
            estimated_distance = min(10000, max(1000, abs(avg_doppler) * c / fo * 5000))
            initial_pos = np.array([center_x, center_y, center_z]) + direction * estimated_distance
            initial_pos[2] = max(100, min(5000, initial_pos[2]))  # 限制高度
        else:
            initial_pos = np.array([center_x, center_y, 1000])

        p_est = np.array([initial_pos[0], initial_pos[1], initial_pos[2], fo])

        # 改进的迭代求解
        best_cost = float('inf')
        best_position = p_est[:3].copy()

        for iteration in range(15):  # 增加迭代次数
            xe_hat, ye_hat, ze_hat, fo_hat = p_est

            # 计算几何关系
            diff_X, diff_Y, diff_Z = Px - xe_hat, Py - ye_hat, Pz - ze_hat
            R_hat = np.sqrt(diff_X**2 + diff_Y**2 + diff_Z**2 + 1e-6)

            # 预测多普勒频率
            f_hat = fo_hat - (fo_hat / c) * (Vx * diff_X + Vy * diff_Y + Vz * diff_Z) / R_hat
            res_f = f_obs - f_hat

            # 计算当前代价函数
            current_cost = np.mean(res_f**2)
            if current_cost < best_cost:
                best_cost = current_cost
                best_position = p_est[:3].copy()

            # 数值梯度计算（更稳定）
            eps = 100.0  # 步长
            grad = np.zeros(3)

            for i in range(3):
                p_plus = p_est.copy()
                p_minus = p_est.copy()
                p_plus[i] += eps
                p_minus[i] -= eps

                # 计算正向扰动的代价
                diff_X_plus = Px - p_plus[0]
                diff_Y_plus = Py - p_plus[1]
                diff_Z_plus = Pz - p_plus[2]
                R_plus = np.sqrt(diff_X_plus**2 + diff_Y_plus**2 + diff_Z_plus**2 + 1e-6)
                f_plus = fo - (fo / c) * (Vx * diff_X_plus + Vy * diff_Y_plus + Vz * diff_Z_plus) / R_plus
                cost_plus = np.mean((f_obs - f_plus)**2)

                # 计算负向扰动的代价
                diff_X_minus = Px - p_minus[0]
                diff_Y_minus = Py - p_minus[1]
                diff_Z_minus = Pz - p_minus[2]
                R_minus = np.sqrt(diff_X_minus**2 + diff_Y_minus**2 + diff_Z_minus**2 + 1e-6)
                f_minus = fo - (fo / c) * (Vx * diff_X_minus + Vy * diff_Y_minus + Vz * diff_Z_minus) / R_minus
                cost_minus = np.mean((f_obs - f_minus)**2)

                # 数值梯度
                grad[i] = (cost_plus - cost_minus) / (2 * eps)

            # 自适应学习率
            if iteration < 5:
                learning_rate = 500.0
            elif iteration < 10:
                learning_rate = 200.0
            else:
                learning_rate = 50.0

            # 梯度下降更新
            p_est[:3] -= learning_rate * grad

            # 约束位置在合理范围内
            p_est[0] = np.clip(p_est[0], -50000, 50000)
            p_est[1] = np.clip(p_est[1], -50000, 50000)
            p_est[2] = np.clip(p_est[2], 50, 10000)

            # 检查收敛
            if np.linalg.norm(grad) < 1e-6 or current_cost < 1.0:
                break

        return best_position

    except Exception:
        # 如果出现任何错误，返回平台中心位置
        Px, Py, Pz = Plat_Nav_Data[:3]
        return np.array([np.mean(Px), np.mean(Py), 1000.0])

def calculate_measurements_for_jacobian(params, Plat_Nav_Data, mu_vect, fo, L=1.0, d=0.5):
    """为雅可比矩阵计算辅助函数"""
    c = 2.998e8; lambda_ = c/fo
    xe, ye, ze, fo_est = params
    Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2 + 1e-9)
    f = fo_est - (fo_est / c) * (Vx * (Px - xe) + Vy * (Py - ye) + Vz * (Pz - ze)) / R
    cos_theta = -np.dot(mu_vect.T, ((Plat_Nav_Data[:3].T - params[:3]) / R[:, np.newaxis]).T).diagonal()
    phase_diff = (2 * np.pi * d) * cos_theta
    phi = -phase_diff * (L / (d*lambda_)) if d > 0 else np.zeros_like(f)
    return f, phi

# ==============================================================================
# 4. 混合循环神经网络模型 (Hybrid Recurrent Neural Network)
# ==============================================================================

def build_recurrent_model(seq_len, num_features, traditional_input_shape=3):
    """(保持不变) 混合RNN模型"""
    sequence_input = Input(shape=(seq_len, num_features), name='sequence_input')
    traditional_input = Input(shape=(traditional_input_shape,), name='traditional_input')
    lstm_out = LSTM(128, return_sequences=False)(sequence_input)
    lstm_out = BatchNormalization()(lstm_out)
    lstm_out = Dropout(0.3)(lstm_out)
    combined = Concatenate()([lstm_out, traditional_input])
    x = Dense(128, activation='relu')(combined)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)
    x = Dense(64, activation='relu')(x)
    output = Dense(3, name='position_output')(x)
    model = Model(inputs=[sequence_input, traditional_input], outputs=output)
    optimizer = Adam(learning_rate=0.001)
    model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])
    return model


def generate_sequence_training_data(num_scenarios=2000, scene_size=2000, snr_range=(12, 25)):
    """(已修正) 生成序列化的训练数据"""
    print(f"正在生成 {num_scenarios} 个序列化的训练场景...")
    X_seq, X_trad, Y_pos = [], [], []
    fo = 1e9

    valid_scenarios = 0
    attempts = 0

    while valid_scenarios < num_scenarios and attempts < num_scenarios * 2:
        attempts += 1

        try:
            g = np.random.uniform(1.5, 3.0)
            T = np.random.uniform(10, 20)
            del_T = 0.2
            alt_kft = np.random.uniform(8, 12)
            vel = np.random.uniform(180, 250)
            snr_db = np.random.uniform(*snr_range)

            x_true = np.random.uniform(-scene_size/2, scene_size/2)
            y_true = np.random.uniform(-scene_size/2, scene_size/2)
            z_true = np.random.uniform(50, scene_size/4)  # 更合理的高度范围
            p_true = np.array([x_true, y_true, z_true, fo])

            Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
            Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

            # 确保有足够的测量点
            if len(Px) < 5:
                continue

            measurement_obs = []
            valid_measurements = 0

            for j in range(len(Px)):
                platform_pos = np.array([Px[j], Py[j], Pz[j]])
                platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
                mu_vect_single = mu_vect[:, j]

                # 检查几何条件
                distance_to_target = np.linalg.norm(platform_pos - p_true[:3])
                if distance_to_target < 500 or distance_to_target > 15000:
                    continue

                iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
                f, cr, ci = extract_features_from_iq(iq_data, fo, fs=2e4)

                # 数据质量检查
                if (not np.isnan(f) and not np.isinf(f) and
                    not np.isnan(cr) and not np.isinf(cr) and
                    not np.isnan(ci) and not np.isinf(ci) and
                    abs(f - fo) < 1000):  # 频率变化应该合理
                    measurement_obs.append([f, cr, ci])
                    valid_measurements += 1

            # 确保有足够的有效测量
            if valid_measurements < 5:
                continue

            measurement_obs = np.array(measurement_obs)

            # 传统方法求解
            p_trad = traditional_solver(measurement_obs, Plat_Nav_Data, mu_vect, fo, d=0.5)

            # 检查传统方法结果的合理性
            if (not np.any(np.isnan(p_trad)) and not np.any(np.isinf(p_trad)) and
                np.linalg.norm(p_trad) < 50000):  # 位置应该在合理范围内

                X_seq.append(measurement_obs)
                X_trad.append(p_trad)
                Y_pos.append(p_true[:3])
                valid_scenarios += 1

                if valid_scenarios % 100 == 0:
                    print(f"已生成 {valid_scenarios}/{num_scenarios} 个有效场景")

        except Exception as e:
            continue

    print(f"数据生成完成。有效场景: {valid_scenarios}, 总尝试: {attempts}")

    if valid_scenarios == 0:
        raise ValueError("无法生成有效的训练数据")

    # 确保所有序列长度一致
    min_length = min(len(seq) for seq in X_seq)
    max_length = max(len(seq) for seq in X_seq)

    if min_length != max_length:
        print(f"序列长度不一致，将截断到最小长度: {min_length}")
        X_seq = [seq[:min_length] for seq in X_seq]

    # 转换为numpy数组
    X_seq = np.array(X_seq, dtype=np.float32)
    X_trad = np.array(X_trad, dtype=np.float32)
    Y_pos = np.array(Y_pos, dtype=np.float32)

    print(f"最终数据形状:")
    print(f"X_seq: {X_seq.shape}")
    print(f"X_trad: {X_trad.shape}")
    print(f"Y_pos: {Y_pos.shape}")

    return X_seq, X_trad, Y_pos

def train_recurrent_model(X_seq, X_trad, Y_pos, epochs=100, batch_size=64):
    """(已修正) 训练混合RNN模型"""
    print("开始数据预处理...")
    print(f"输入数据形状: X_seq={X_seq.shape}, X_trad={X_trad.shape}, Y_pos={Y_pos.shape}")

    # 确保数据类型正确
    X_seq = np.array(X_seq, dtype=np.float32)
    X_trad = np.array(X_trad, dtype=np.float32)
    Y_pos = np.array(Y_pos, dtype=np.float32)

    # 数据归一化 - 使用更稳定的方法
    seq_reshaped = X_seq.reshape(-1, X_seq.shape[-1])
    seq_mean = np.mean(seq_reshaped, axis=0)
    seq_std = np.std(seq_reshaped, axis=0)
    seq_std = np.where(seq_std < 1e-8, 1.0, seq_std)  # 避免除零
    X_seq_norm = (X_seq - seq_mean) / seq_std

    trad_mean = np.mean(X_trad, axis=0)
    trad_std = np.std(X_trad, axis=0)
    trad_std = np.where(trad_std < 1e-8, 1.0, trad_std)  # 避免除零
    X_trad_norm = (X_trad - trad_mean) / trad_std

    pos_mean = np.mean(Y_pos, axis=0)
    pos_std = np.std(Y_pos, axis=0)
    pos_std = np.where(pos_std < 1e-8, 1000.0, pos_std)  # 避免除零，默认1km标准差
    Y_pos_norm = (Y_pos - pos_mean) / pos_std

    norm_params = {
        'seq_mean': seq_mean, 'seq_std': seq_std,
        'trad_mean': trad_mean, 'trad_std': trad_std,
        'pos_mean': pos_mean, 'pos_std': pos_std,
    }
    np.save('nn_normalization_params_v5.npy', norm_params)
    print("归一化参数已保存到 nn_normalization_params_v5.npy")

    model = build_recurrent_model(X_seq.shape[1], X_seq.shape[2])
    model.summary()

    callbacks = [
        EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True, verbose=1),
        ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=7, min_lr=1e-7, verbose=1),
        ModelCheckpoint('best_location_model_v5.keras', monitor='val_loss', save_best_only=True)
    ]

    print("开始训练混合RNN模型...")
    model.fit(
        [X_seq_norm, X_trad_norm],
        Y_pos_norm,
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )
    print("模型训练完成。")


# ==============================================================================
# 5. 主流程、绘图与精度评估
# ==============================================================================

def run_accuracy_test(num_tests=200, snr_db=12, scene_size=2000):
    """(已修正) 运行精度评估测试"""
    model_path = 'best_location_model_v5.keras'
    norm_path = 'nn_normalization_params_v5.npy'
    if not os.path.exists(model_path):
        print(f"错误：找不到模型 '{model_path}'。请先使用 --train 参数训练。")
        return

    print("加载预训练的混合RNN模型...")
    model = tf.keras.models.load_model(model_path)
    norm_params = np.load(norm_path, allow_pickle=True).item()

    # 获取模型期望的序列长度
    expected_seq_length = model.input[0].shape[1]  # 从模型输入形状获取
    print(f"模型期望的序列长度: {expected_seq_length}")

    errors_trad, errors_hybrid = [], []
    successful_trad, successful_hybrid = 0, 0

    # 调试计数器
    debug_counters = {
        'total_attempts': 0,
        'insufficient_measurements': 0,
        'geometry_filtered': 0,
        'data_quality_failed': 0,
        'traditional_failed': 0,
        'hybrid_failed': 0,
        'successful': 0
    }

    for test_idx in tqdm(range(num_tests), desc="精度评估中"):
        try:
            debug_counters['total_attempts'] += 1

            g = np.random.uniform(1.5, 3.0); T = np.random.uniform(10, 20); del_T = 0.2
            alt_kft = np.random.uniform(8, 12); vel = np.random.uniform(180, 250); fo = 1e9

            x_true = np.random.uniform(-scene_size/2, scene_size/2)
            y_true = np.random.uniform(-scene_size/2, scene_size/2)
            z_true = np.random.uniform(50, scene_size/4)  # 更合理的高度范围
            p_true = np.array([x_true, y_true, z_true, fo])

            Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
            Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])

            measurement_obs = []
            valid_measurements = 0
            geometry_filtered = 0

            for j in range(len(Px)):
                platform_pos = np.array([Px[j], Py[j], Pz[j]])
                platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
                mu_vect_single = mu_vect[:, j]

                # 放宽几何条件检查
                distance_to_target = np.linalg.norm(platform_pos - p_true[:3])
                if distance_to_target < 200 or distance_to_target > 25000:  # 放宽距离限制
                    geometry_filtered += 1
                    continue

                iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
                f, cr, ci = extract_features_from_iq(iq_data, fo, fs=2e4)

                # 放宽数据质量检查
                if (not np.isnan(f) and not np.isinf(f) and
                    not np.isnan(cr) and not np.isinf(cr) and
                    not np.isnan(ci) and not np.isinf(ci) and
                    abs(f - fo) < 5000):  # 放宽频率变化限制
                    measurement_obs.append([f, cr, ci])
                    valid_measurements += 1

            # 降低最小测量数要求
            if valid_measurements < 3:  # 从5降低到3
                debug_counters['insufficient_measurements'] += 1
                continue

            debug_counters['geometry_filtered'] += geometry_filtered

            measurement_obs = np.array(measurement_obs)

            # 调整序列长度以匹配模型期望
            if len(measurement_obs) > expected_seq_length:
                # 如果测量点太多，随机选择
                indices = np.random.choice(len(measurement_obs), expected_seq_length, replace=False)
                measurement_obs = measurement_obs[indices]
            elif len(measurement_obs) < expected_seq_length:
                # 如果测量点太少，重复最后一个测量
                padding_needed = expected_seq_length - len(measurement_obs)
                last_measurement = measurement_obs[-1:].repeat(padding_needed, axis=0)
                measurement_obs = np.vstack([measurement_obs, last_measurement])

            # 1. 传统方法预测
            try:
                p_trad = traditional_solver(measurement_obs, Plat_Nav_Data, mu_vect, fo, d=0.5)
            except Exception:
                debug_counters['traditional_failed'] += 1
                continue

            # 放宽传统方法结果的合理性检查
            if np.any(np.isnan(p_trad)) or np.any(np.isinf(p_trad)) or np.linalg.norm(p_trad) > 100000:  # 放宽到100km
                debug_counters['traditional_failed'] += 1
                continue

            # 2. 混合RNN方法预测
            try:
                seq_norm = (measurement_obs - norm_params['seq_mean']) / (norm_params['seq_std'] + 1e-8)
                trad_norm = (p_trad - norm_params['trad_mean']) / (norm_params['trad_std'] + 1e-8)

                # 确保输入形状正确
                seq_input = np.expand_dims(seq_norm, axis=0)  # (1, seq_len, 3)
                trad_input = np.expand_dims(trad_norm, axis=0)  # (1, 3)

                p_hybrid_norm = model.predict([seq_input, trad_input], verbose=0)
                p_hybrid = p_hybrid_norm * norm_params['pos_std'] + norm_params['pos_mean']
                p_hybrid = p_hybrid.flatten()
            except Exception:
                debug_counters['hybrid_failed'] += 1
                continue

            # 检查混合方法结果的合理性
            if np.any(np.isnan(p_hybrid)) or np.any(np.isinf(p_hybrid)):
                debug_counters['hybrid_failed'] += 1
                continue

            # 计算误差
            error_trad = np.linalg.norm(p_trad - p_true[:3])
            error_hybrid = np.linalg.norm(p_hybrid - p_true[:3])
            errors_trad.append(error_trad)
            errors_hybrid.append(error_hybrid)

            true_distance = np.linalg.norm(p_true[:3])
            error_threshold = max(100.0, 0.05 * true_distance)
            if error_trad <= error_threshold: successful_trad += 1
            if error_hybrid <= error_threshold: successful_hybrid += 1

            debug_counters['successful'] += 1

        except Exception as e:
            # 跳过有问题的测试案例
            debug_counters['data_quality_failed'] += 1
            continue
    
    print(f"\n调试信息: 成功测试数量 = {len(errors_trad)}/{num_tests}")
    print("详细调试统计:")
    print(f"  总尝试次数: {debug_counters['total_attempts']}")
    print(f"  测量不足: {debug_counters['insufficient_measurements']}")
    print(f"  几何过滤: {debug_counters['geometry_filtered']}")
    print(f"  传统方法失败: {debug_counters['traditional_failed']}")
    print(f"  混合方法失败: {debug_counters['hybrid_failed']}")
    print(f"  数据质量失败: {debug_counters['data_quality_failed']}")
    print(f"  成功案例: {debug_counters['successful']}")

    if len(errors_trad) == 0:
        print("❌ 错误：没有成功的测试案例！")
        return

    print("\n" + "="*50)
    print(" 精  度  评  估  报  告 (v5 - Hybrid RNN)")
    print("="*50)
    print(f"场景大小: {scene_size}m, 信噪比: {snr_db}dB, 测试次数: {num_tests}")
    print(f"有效测试次数: {len(errors_trad)}")
    print("-" * 50)

    # 计算准确率基于有效测试次数
    effective_tests = len(errors_trad)
    trad_accuracy = successful_trad / effective_tests if effective_tests > 0 else 0
    hybrid_accuracy = successful_hybrid / effective_tests if effective_tests > 0 else 0

    print(f"传统方法: 准确率 {trad_accuracy:.2%}, 平均误差 {np.mean(errors_trad):.2f}m")
    print(f"混合RNN:  准确率 {hybrid_accuracy:.2%}, 平均误差 {np.mean(errors_hybrid):.2f}m")
    print("-" * 50)
    if hybrid_accuracy >= 0.85:
        print("✅ 目标达成：混合RNN模型定位准确率不低于85%。")
    else:
        print("❌ 目标未达成：混合RNN模型定位准确率低于85%。")
    print("="*50)

    # 只有在有数据时才绘图
    try:
        plt.figure(figsize=(12, 6))
        if len(errors_trad) > 0:
            plt.hist(errors_trad, bins=min(20, len(errors_trad)), alpha=0.6,
                    label=f'传统方法 (均值: {np.mean(errors_trad):.0f}m)', color='royalblue')
        if len(errors_hybrid) > 0:
            plt.hist(errors_hybrid, bins=min(20, len(errors_hybrid)), alpha=0.8,
                    label=f'混合RNN (均值: {np.mean(errors_hybrid):.0f}m)', color='darkorange')
        plt.title(f'误差分布对比 (SNR={snr_db}dB)')
        plt.xlabel('定位误差 (m)'); plt.ylabel('次数'); plt.legend(); plt.grid(True, alpha=0.3)
        plt.savefig('accuracy_test_error_distribution_v5.png')
        print("图表已保存为 accuracy_test_error_distribution_v5.png")
        # plt.show()  # 注释掉以避免阻塞
    except Exception as e:
        print(f"绘图时出错: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='基于IQ数据的辐射源定位系统 (v5 - 混合RNN版)')
    parser.add_argument('--train', action='store_true', help='强制重新训练模型。')
    parser.add_argument('--test-accuracy', action='store_true', help='运行精度评估测试。')
    parser.add_argument('--snr', type=float, default=12.0, help='设置测试的信噪比(dB)。')
    parser.add_argument('--num-tests', type=int, default=200, help='设置精度评估的测试次数。')

    args = parser.parse_args()

    if not USE_TENSORFLOW:
        sys.exit("错误：此脚本需要TensorFlow。")

    if args.train:
        print("开始训练流程...")
        X_seq, X_trad, Y_pos = generate_sequence_training_data(num_scenarios=4000)
        train_recurrent_model(X_seq, X_trad, Y_pos, epochs=120, batch_size=64)
    
    elif args.test_accuracy:
        run_accuracy_test(num_tests=args.num_tests, snr_db=args.snr)
        
    else:
        print("此脚本的主要功能是训练(--train)和评估(--test-accuracy)。")
        print("要运行单次定位演示，请取消注释或修改主函数部分的代码。")
