import numpy as np
from scipy.optimize import least_squares

def simple_test():
    """简单测试传统定位方法"""
    print("开始简单测试...")
    
    # 模拟一些测量数据
    measurement_obs = np.array([
        [1e9 + 100, 0.5, 0.3, 0.2, 15, 0.8, 0.7, 1.0, 0.9, 0.1, 0.05],
        [1e9 + 120, 0.6, 0.4, 0.3, 16, 0.7, 0.8, 1.1, 0.8, 0.2, 0.06],
        [1e9 + 80, 0.4, 0.2, 0.1, 14, 0.9, 0.6, 0.9, 0.7, 0.15, 0.04]
    ])
    
    # 模拟平台数据
    Plat_Nav_Data = np.array([
        [1000, 2000, 3000],  # X位置
        [1500, 2500, 3500],  # Y位置
        [3000, 3000, 3000],  # Z位置
        [100, 120, 80],      # X速度
        [50, 60, 40],        # Y速度
        [0, 0, 0]            # Z速度
    ])
    
    # 模拟mu_vect
    mu_vect = np.array([
        [1, 0, -1],
        [0, 1, 0],
        [0, 0, 0]
    ])
    
    fo = 1e9
    
    # 测试传统定位方法
    try:
        from IQ_test import traditional_localization
        result, cost = traditional_localization(measurement_obs, Plat_Nav_Data, mu_vect, fo)
        print(f"传统方法结果: {result}")
        print(f"成本: {cost}")
        
        # 检查结果是否合理
        if np.all(np.abs(result) < 50000):  # 50km范围内
            print("✅ 传统方法结果合理")
        else:
            print("❌ 传统方法结果不合理")
            
    except Exception as e:
        print(f"传统方法测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
