import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.optimize import least_squares
import sys
import io
import os
import argparse
import time
from tqdm import tqdm

# 解决控制台输出编码问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 尝试导入TensorFlow
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, GaussianNoise
    from tensorflow.keras.layers import Conv1D, GlobalMaxPooling1D, LSTM, Bidirectional, Attention
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow。")
except ImportError:
    USE_TENSORFLOW = False
    print("未能加载TensorFlow，将只使用传统方法。")

# ==============================================================================
# 1. 平台运动与几何关系 (Platform Motion and Geometry)
# ==============================================================================

def weave(g, T, del_T, alt_kft, vel):
    """平台导航数据生成函数"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    accelv = 0.5 * 9.81
    alt0 = alt_kft * 1000 * 0.3048

    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    gamma0 = -np.arcsin(min(accelv*turndur/vel, 0.5))/2

    px, py, pz = 0, 0, alt0
    XYZA, XYZADOT, ah = [], [], []
    itt = 0

    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
            ahoriz = accelh
            az = accelv
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
            ahoriz = -accelh
            az = -accelv

        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)

        if itt > 0:
            vz = XYZADOT[-1][2] + az * dtt if XYZADOT else vel * np.sin(gamma0)
            px += vx * dtt
            py += vy * dtt
            pz += vz * dtt
        else:
            vz = vel * np.sin(gamma0)

        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
            ah.append(ahoriz)

        itt += 1

    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    ah = np.array(ah)

    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    long_vect = np.vstack([np.sin(hdg), np.cos(hdg), np.zeros_like(hdg)])
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / vect_norms

    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect

# ==============================================================================
# 2. 端到端IQ数据生成与处理 (End-to-End IQ Data Generation and Processing)
# ==============================================================================

def generate_enhanced_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db, L=4, d=0.5, fs=2e4, num_samples=1024):
    """端到端增强IQ数据生成 - 更多通道和更真实的信号模型"""
    c = 2.998e8
    lambda_ = c / fo
    xe, ye, ze, fo_true = p_true
    Px, Py, Pz = platform_pos
    Vx, Vy, Vz = platform_vel

    R_vec = np.array([Px - xe, Py - ye, Pz - ze])
    R = np.linalg.norm(R_vec)
    los_vec_norm = R_vec / R

    # 多普勒频率计算
    doppler_freq = -(fo / c) * np.dot(platform_vel, los_vec_norm)
    cos_theta = -np.dot(los_vec_norm, mu_vect_single)

    t = np.arange(num_samples) / fs
    signal_power = 1.0
    iq_data = np.zeros((L, num_samples), dtype=np.complex128)

    # 更真实的信号参数
    freq_stability = 1e-9  # 更好的频率稳定度
    
    # 生成更真实的频率和相位噪声
    freq_drift = np.random.normal(0, freq_stability * fo)
    
    for i in range(L):
        # 阵列几何 - 使用更复杂的阵列配置
        if L == 4:  # 2x2阵列
            x_pos = (i % 2) * d
            y_pos = (i // 2) * d
            phase_shift = (2 * np.pi / lambda_) * (x_pos * np.sin(np.arccos(cos_theta)) * np.cos(0) + 
                                                   y_pos * np.sin(np.arccos(cos_theta)) * np.sin(0))
        else:  # 线性阵列
            phase_shift = (2 * np.pi * d * i / lambda_) * cos_theta
        
        # 改进的相位噪声模型
        white_noise = np.random.normal(0, 1, num_samples)
        phase_noise = np.cumsum(white_noise) * 0.0005  # 更小的相位噪声
        
        # 振荡器相位噪声
        osc_phase_noise = np.random.normal(0, 0.002, num_samples)
        total_phase_noise = phase_noise + osc_phase_noise
        
        # 生成基带信号
        instantaneous_freq = doppler_freq + freq_drift
        instantaneous_phase = 2 * np.pi * instantaneous_freq * t + phase_shift + total_phase_noise
        signal = np.exp(1j * instantaneous_phase)
        
        # 添加更真实的幅度效应
        path_loss_variation = 1.0 + 0.01 * np.random.normal(0, 1)
        
        # 快衰落效应（简化）
        fading_freq = 5  # Hz
        fading_amplitude = 0.05
        fading = 1.0 + fading_amplitude * np.sin(2 * np.pi * fading_freq * t + np.random.uniform(0, 2*np.pi))
        
        # 接收机增益变化
        gain_variation = 1.0 + 0.005 * np.random.normal(0, 1, num_samples)
        
        # 组合所有幅度效应
        amplitude_effects = path_loss_variation * fading * gain_variation
        signal = signal * amplitude_effects
        
        iq_data[i, :] = signal

    # 改进的噪声模型
    snr_linear = 10**(snr_db / 10.0)
    noise_power = signal_power / snr_linear
    noise_std = np.sqrt(noise_power / 2)

    # 更真实的噪声特性
    if L > 1:
        # 通道间相关噪声
        correlation_coeff = 0.1
        
        # 生成相关噪声
        independent_noise = (np.random.normal(0, noise_std * np.sqrt(1 - correlation_coeff), size=iq_data.shape) +
                           1j * np.random.normal(0, noise_std * np.sqrt(1 - correlation_coeff), size=iq_data.shape))
        
        common_noise_real = np.random.normal(0, noise_std * np.sqrt(correlation_coeff), size=(1, num_samples))
        common_noise_imag = np.random.normal(0, noise_std * np.sqrt(correlation_coeff), size=(1, num_samples))
        common_noise = common_noise_real + 1j * common_noise_imag
        common_noise = np.repeat(common_noise, L, axis=0)
        
        noise = independent_noise + common_noise
    else:
        noise = (np.random.normal(0, noise_std, size=iq_data.shape) +
                1j * np.random.normal(0, noise_std, size=iq_data.shape))

    # 添加量化噪声（ADC效应）
    adc_bits = 14  # 14位ADC
    adc_range = 2.0
    quantization_step = adc_range / (2**adc_bits)
    quantization_noise = (np.random.uniform(-0.5, 0.5, size=iq_data.shape) + 
                         1j * np.random.uniform(-0.5, 0.5, size=iq_data.shape)) * quantization_step

    return iq_data + noise + quantization_noise

# ==============================================================================
# 3. 端到端深度学习模型 (End-to-End Deep Learning Model)
# ==============================================================================

def build_end_to_end_model(num_channels=4, num_samples=1024, platform_features=6):
    """构建端到端深度学习模型 - 直接从IQ数据到位置"""
    
    # === 输入层 ===
    # IQ数据输入 - 复数数据分为实部和虚部
    iq_real_input = Input(shape=(num_channels, num_samples), name='iq_real_input')
    iq_imag_input = Input(shape=(num_channels, num_samples), name='iq_imag_input')
    platform_input = Input(shape=(platform_features,), name='platform_input')
    
    # === IQ数据处理分支 ===
    # 处理实部
    real_conv1 = Conv1D(32, 64, activation='relu', padding='same')(iq_real_input)
    real_conv1 = BatchNormalization()(real_conv1)
    real_conv2 = Conv1D(64, 32, activation='relu', padding='same')(real_conv1)
    real_conv2 = BatchNormalization()(real_conv2)
    real_pool = GlobalMaxPooling1D()(real_conv2)
    
    # 处理虚部
    imag_conv1 = Conv1D(32, 64, activation='relu', padding='same')(iq_imag_input)
    imag_conv1 = BatchNormalization()(imag_conv1)
    imag_conv2 = Conv1D(64, 32, activation='relu', padding='same')(imag_conv1)
    imag_conv2 = BatchNormalization()(imag_conv2)
    imag_pool = GlobalMaxPooling1D()(imag_conv2)
    
    # 合并实部和虚部特征
    iq_features = Concatenate()([real_pool, imag_pool])
    iq_features = Dense(256, activation='relu')(iq_features)
    iq_features = BatchNormalization()(iq_features)
    iq_features = Dropout(0.3)(iq_features)
    
    # === 平台数据处理分支 ===
    platform_features_processed = Dense(64, activation='relu')(platform_input)
    platform_features_processed = BatchNormalization()(platform_features_processed)
    platform_features_processed = Dense(32, activation='relu')(platform_features_processed)
    platform_features_processed = BatchNormalization()(platform_features_processed)
    
    # === 特征融合 ===
    combined_features = Concatenate()([iq_features, platform_features_processed])
    
    # === 深层网络 ===
    x = Dense(512, activation='relu')(combined_features)
    x = BatchNormalization()(x)
    x = Dropout(0.4)(x)
    
    x = Dense(256, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)
    
    x = Dense(128, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.2)(x)
    
    x = Dense(64, activation='relu')(x)
    x = BatchNormalization()(x)
    x = Dropout(0.1)(x)
    
    # === 输出层 ===
    position_output = Dense(3, activation='linear', name='position_output')(x)
    
    # === 构建模型 ===
    model = Model(inputs=[iq_real_input, iq_imag_input, platform_input], outputs=position_output)
    
    # === 损失函数 ===
    def enhanced_position_loss(y_true, y_pred):
        # 基础位置误差
        position_error = y_true - y_pred
        
        # 1. Huber损失
        huber_delta = 200.0  # 200米阈值
        abs_error = tf.abs(position_error)
        quadratic = tf.minimum(abs_error, huber_delta)
        linear = abs_error - quadratic
        huber_loss = 0.5 * quadratic**2 + huber_delta * linear
        
        # 2. 相对误差损失
        distance_true = tf.sqrt(tf.reduce_sum(y_true**2, axis=1, keepdims=True))
        relative_error = tf.abs(position_error) / (distance_true + 200.0)
        relative_loss = tf.reduce_mean(relative_error)
        
        # 3. 组合损失
        total_loss = tf.reduce_mean(huber_loss) + 0.05 * relative_loss
        
        return total_loss
    
    # === 编译模型 ===
    optimizer = Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999, epsilon=1e-7)
    model.compile(optimizer=optimizer, loss=enhanced_position_loss, metrics=['mae', 'mse'])
    
    return model

# ==============================================================================
# 4. 端到端训练数据生成 (End-to-End Training Data Generation)
# ==============================================================================

def generate_end_to_end_training_data(num_scenarios=1000, snr_range=(12, 25), area_size=5000):
    """生成端到端训练数据 - 直接从IQ数据到位置"""
    print(f"正在生成 {num_scenarios} 个场景的端到端训练数据...")

    IQ_real_data, IQ_imag_data, Platform_data, Position_data = [], [], [], []
    del_T = 0.1
    fo = 1e9
    L = 4  # 4通道阵列
    num_samples = 1024  # 更少的样本数以加快训练

    for scenario_idx in tqdm(range(num_scenarios), desc="生成训练数据"):
        # 平台参数
        g = np.random.uniform(1.5, 3.5)
        T = np.random.uniform(8, 15)
        alt_kft = np.random.uniform(8, 12)
        vel = np.random.uniform(180, 250)

        # 目标位置
        x_true = np.random.uniform(-area_size, area_size)
        y_true = np.random.uniform(-area_size, area_size)
        z_true = np.random.uniform(0, 800)
        fo_true = fo * np.random.uniform(0.9995, 1.0005)
        p_true = np.array([x_true, y_true, z_true, fo_true])

        # SNR
        snr_db = np.random.uniform(*snr_range)

        # 生成平台轨迹
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)

        # 随机选择几个平台位置
        num_positions = len(Px)
        selected_indices = np.random.choice(num_positions,
                                          size=min(10, num_positions),
                                          replace=False)

        for j in selected_indices:
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
            mu_vect_single = mu_vect[:, j]

            # 生成IQ数据
            iq_data = generate_enhanced_iq_data(p_true, platform_pos, platform_vel,
                                              mu_vect_single, fo, snr_db, L=L,
                                              num_samples=num_samples)

            # 分离实部和虚部
            iq_real = np.real(iq_data)
            iq_imag = np.imag(iq_data)

            # 平台特征
            platform_features = np.array([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])

            # 数据质量检查
            if (not np.any(np.isnan(iq_real)) and not np.any(np.isinf(iq_real)) and
                not np.any(np.isnan(iq_imag)) and not np.any(np.isinf(iq_imag))):
                IQ_real_data.append(iq_real)
                IQ_imag_data.append(iq_imag)
                Platform_data.append(platform_features)
                Position_data.append(p_true[:3])  # 只使用位置信息

    print(f"数据生成完成。总样本数: {len(IQ_real_data)}")

    return (np.array(IQ_real_data), np.array(IQ_imag_data),
            np.array(Platform_data), np.array(Position_data))

def train_end_to_end_model(IQ_real, IQ_imag, Platform, Positions, epochs=50, batch_size=32):
    """训练端到端模型"""
    print("开始端到端模型训练...")

    # === 数据归一化 ===
    # IQ数据归一化
    iq_real_mean = IQ_real.mean()
    iq_real_std = IQ_real.std()
    IQ_real_norm = (IQ_real - iq_real_mean) / (iq_real_std + 1e-8)

    iq_imag_mean = IQ_imag.mean()
    iq_imag_std = IQ_imag.std()
    IQ_imag_norm = (IQ_imag - iq_imag_mean) / (iq_imag_std + 1e-8)

    # 平台数据归一化
    platform_mean = Platform.mean(axis=0)
    platform_std = Platform.std(axis=0)
    Platform_norm = (Platform - platform_mean) / (platform_std + 1e-8)

    # 位置数据归一化
    position_mean = Positions.mean(axis=0)
    position_std = Positions.std(axis=0)
    Positions_norm = (Positions - position_mean) / (position_std + 1e-8)

    # 保存归一化参数
    normalization_params = {
        'iq_real_mean': iq_real_mean, 'iq_real_std': iq_real_std,
        'iq_imag_mean': iq_imag_mean, 'iq_imag_std': iq_imag_std,
        'platform_mean': platform_mean, 'platform_std': platform_std,
        'position_mean': position_mean, 'position_std': position_std
    }
    np.save('end_to_end_normalization_params.npy', normalization_params)
    print("归一化参数已保存")

    # === 构建模型 ===
    num_channels, num_samples = IQ_real.shape[1], IQ_real.shape[2]
    platform_features = Platform.shape[1]

    model = build_end_to_end_model(num_channels, num_samples, platform_features)
    model.summary()

    # === 回调函数 ===
    callbacks = [
        EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True, verbose=1),
        ReduceLROnPlateau(monitor='val_loss', factor=0.7, patience=8, min_lr=1e-7, verbose=1),
        ModelCheckpoint('best_end_to_end_model.keras', monitor='val_loss', save_best_only=True, verbose=1)
    ]

    # === 训练 ===
    print(f"开始训练，共 {epochs} 轮...")
    history = model.fit(
        [IQ_real_norm, IQ_imag_norm, Platform_norm],
        Positions_norm,
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )

    print("端到端模型训练完成")
    return model, normalization_params, history

def end_to_end_prediction(model, iq_real, iq_imag, platform_data, normalization_params):
    """端到端预测"""
    # 归一化
    iq_real_norm = (iq_real - normalization_params['iq_real_mean']) / normalization_params['iq_real_std']
    iq_imag_norm = (iq_imag - normalization_params['iq_imag_mean']) / normalization_params['iq_imag_std']
    platform_norm = (platform_data - normalization_params['platform_mean']) / normalization_params['platform_std']

    # 预测
    prediction_norm = model.predict([iq_real_norm, iq_imag_norm, platform_norm], verbose=0)

    # 反归一化
    prediction = prediction_norm * normalization_params['position_std'] + normalization_params['position_mean']

    return prediction

# ==============================================================================
# 5. 主程序 (Main Program)
# ==============================================================================

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='端到端IQ数据定位系统')
    parser.add_argument('--train', action='store_true', help='训练端到端模型')
    parser.add_argument('--test', action='store_true', help='测试端到端模型')
    parser.add_argument('--snr', type=float, default=15.0, help='信噪比(dB)')
    parser.add_argument('--num-tests', type=int, default=100, help='测试次数')

    args = parser.parse_args()

    if not USE_TENSORFLOW:
        print("错误：需要TensorFlow")
        sys.exit(1)

    if args.train:
        print("开始端到端训练...")
        IQ_real, IQ_imag, Platform, Positions = generate_end_to_end_training_data(
            num_scenarios=300, snr_range=(12, 25))
        train_end_to_end_model(IQ_real, IQ_imag, Platform, Positions, epochs=50, batch_size=16)

    elif args.test:
        print("端到端模型测试...")

        # 定义自定义损失函数用于加载模型
        def enhanced_position_loss(y_true, y_pred):
            position_error = y_true - y_pred
            huber_delta = 200.0
            abs_error = tf.abs(position_error)
            quadratic = tf.minimum(abs_error, huber_delta)
            linear = abs_error - quadratic
            huber_loss = 0.5 * quadratic**2 + huber_delta * linear
            distance_true = tf.sqrt(tf.reduce_sum(y_true**2, axis=1, keepdims=True))
            relative_error = tf.abs(position_error) / (distance_true + 200.0)
            relative_loss = tf.reduce_mean(relative_error)
            total_loss = tf.reduce_mean(huber_loss) + 0.05 * relative_loss
            return total_loss

        # 加载模型
        try:
            model = tf.keras.models.load_model('best_end_to_end_model.keras',
                                             custom_objects={'enhanced_position_loss': enhanced_position_loss})
            norm_params = np.load('end_to_end_normalization_params.npy', allow_pickle=True).item()
            print("成功加载端到端模型")
        except:
            print("无法加载模型，请先训练")
            sys.exit(1)

        # 运行测试
        print(f"开始精度评估测试，共 {args.num_tests} 次，SNR = {args.snr} dB...")
        print("标准：误差 <= max(100m, 5% of 真实距离) 视为成功。")

        successful_localizations = 0
        errors = []

        for test_idx in tqdm(range(args.num_tests), desc="精度评估中"):
            # 生成测试场景
            area_size = 7000
            x_true = np.random.uniform(-area_size, area_size)
            y_true = np.random.uniform(-area_size, area_size)
            z_true = np.random.uniform(0, 500)
            fo_true = 1e9 * np.random.uniform(0.9995, 1.0005)
            p_true = np.array([x_true, y_true, z_true, fo_true])

            # 生成平台轨迹
            g, T, del_T, alt_kft, vel = 2, 10, 0.1, 10, 200
            Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)

            # 收集IQ数据
            iq_real_list, iq_imag_list, platform_list = [], [], []

            for j in range(len(Px)):
                platform_pos = np.array([Px[j], Py[j], Pz[j]])
                platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
                mu_vect_single = mu_vect[:, j]

                iq_data = generate_enhanced_iq_data(p_true, platform_pos, platform_vel,
                                                  mu_vect_single, 1e9, args.snr, L=4, num_samples=1024)

                iq_real_list.append(np.real(iq_data))
                iq_imag_list.append(np.imag(iq_data))
                platform_list.append([Px[j], Py[j], Pz[j], Vx[j], Vy[j], Vz[j]])

            # 转换为numpy数组
            iq_real_array = np.array(iq_real_list)
            iq_imag_array = np.array(iq_imag_list)
            platform_array = np.array(platform_list)

            # 端到端预测
            predictions = end_to_end_prediction(model, iq_real_array, iq_imag_array, platform_array, norm_params)

            # 使用加权平均融合多个预测
            if len(predictions) > 1:
                final_pos = np.median(predictions, axis=0)  # 使用中位数更鲁棒
            else:
                final_pos = predictions[0] if len(predictions) > 0 else np.array([0, 0, 0])

            # 计算误差
            error = np.linalg.norm(final_pos - p_true[:3])
            true_distance = np.linalg.norm(p_true[:3])
            error_threshold = max(100.0, 0.05 * true_distance)
            errors.append(error)

            if error <= error_threshold:
                successful_localizations += 1

        # 输出结果
        accuracy = (successful_localizations / args.num_tests) * 100

        print("\n" + "="*50)
        print(" 端到端模型精度评估报告")
        print("="*50)
        print(f" 测试总数: {args.num_tests}")
        print(f" 信噪比 (SNR): {args.snr} dB")
        print(f" 成功定位次数: {successful_localizations}")
        print(f" 定位准确率: {accuracy:.2f}%")
        print(f" 平均误差: {np.mean(errors):.2f} m")
        print(f" 中位数误差: {np.median(errors):.2f} m")
        print(f" 最大误差: {np.max(errors):.2f} m")
        print(f" 最小误差: {np.min(errors):.2f} m")
        print("-" * 50)
        if accuracy >= 85.0:
            print("✅ 目标达成：定位准确率不低于85%。")
        else:
            print("❌ 目标未达成：定位准确率低于85%。")
        print("="*50)

    else:
        print("使用 --train 进行训练，--test 进行测试")
